import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import {
  GridCellProps,
  GridColumnProps,
  GridFilterCellProps,
  GridRowClickEvent,
  GridRowProps,
  GridSortChangeEvent,
} from '@progress/kendo-react-grid';

import GridHeader from '../common/GridHeader';
import AddAssessment from './AddAssessment';
import Modal from '../controls/Modal';
import {
  AssessmentApproach,
  AssessmentStatus,
  AssessmentUserRole,
  DialogAction,
  StrategiesEnum,
  UserRoleEnum,
} from '../../models/Enums';
import DataGrid from '../controls/DataGrid';
import { orderBy, SortDescriptor } from '@progress/kendo-data-query';
import {
  AssessmentGridModel,
  assessmentsManual,
} from '../../models/AssessmentModel';
import { assessmentService } from '../../services/assessmentService';

import classes from './assessment.module.scss';
import { UtilityHelper } from '../../utils/UtilityHelper';
import { useNavigate, useLocation } from 'react-router-dom';
import { Backdrop, CircularProgress, Link as MatLink } from '@mui/material';
import { QueryListResultModel } from '../../models/QueryListResultModel';
import useDataGridEvents from '../controls/useDataGridEvents';
import Dropdown from '../controls/Dropdown';
import MultiSelectModel from '../../models/MultiSelectModel';
import { UserAssessmentPermission } from '../../models/PermissionModel';
import DatePicker from '../controls/DatePicker';
// // import { Date | null } from "@material-ui/pickers/typings/date";
import { useDispatch } from 'react-redux';
import { updateStrategySelection } from '../../redux/ducks/scopedefinition';
import { StrategySelectionModel } from '../../models/ScopeDefinitionModel';
import { setUserAssessmentPermission } from '../../redux/ducks/user-permission';
import { CurrentUserModel } from '../../models/ProfileModel';
import { authService } from '../../services/authService';
import { Constants } from '../../models/Constants';
import { FilterCriteria } from '../../models/GridBaseRequestModel';

/** Renders Assessment list */
const Assessments = () => {
  const { t } = useTranslation();
  document.title = t('app.AssessmentsTitle');
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location: any = useLocation();
  const {
    gridRequestModel,
    initialFilter,
    onCustomFilterChange,
    onCustomDateFilterChange,
    onSortChange,
    onFilterChange,
    onPageChange,
  } = useDataGridEvents();

  const [open, setOpen] = useState<boolean>(false);
  const initialSort: Array<SortDescriptor> = [{ field: 'status', dir: 'asc' }];
  const [sort, setSort] = useState<Array<SortDescriptor>>(initialSort);
  const [assessments, setAssessments] = useState<
    QueryListResultModel<AssessmentGridModel>
  >(QueryListResultModel.init());
  const [hover, setHover] = useState<string>('');
  const [assessmentId, setAssessmentId] = useState<string>('');
  const [assessmentStatus, setAssessmentStatus] = useState<number>();
  const currentUser: CurrentUserModel = authService.getCurrentUser();
  let selectedCountryId = sessionStorage.getItem(
    Constants.SessionStorageKey.SELECTED_COUNTRY
  );
  let selectedUserType =
    sessionStorage.getItem(Constants.SessionStorageKey.SELECTED_USER_TYPE) ||
    '';
    const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    bindAssessments();
  }, [gridRequestModel, initialFilter]);

  useEffect(() => {
    // Sets/initializes the user assessment permissions when user lands on the assessments grid screen
    dispatch(setUserAssessmentPermission(UserAssessmentPermission.init()));
  }, []);

  // Check date is valid or not
  const checkDateIsValid = (date: string) => {
    let isValidDate = Date.parse(date);
    return isNaN(isValidDate);
  };

  // bind assessments by calling an api
  const bindAssessments = () => {
    let filterCriteria: Array<FilterCriteria> = [];
    setIsLoading(true);
    // Checks the filter criteria for date if it's valid
    gridRequestModel?.filterCriterias?.forEach((criteria: any) => {
      if (criteria.field == 'startDate' || criteria.field == 'endDate') {
        if (!checkDateIsValid(criteria.value)) {
          filterCriteria = [...filterCriteria, criteria];
        }
      } else {
        filterCriteria = [...filterCriteria, criteria];
      }
    });
    gridRequestModel.filterCriterias = filterCriteria;

    gridRequestModel.countryId = selectedCountryId || '';
    assessmentService
      .getAssessments(gridRequestModel)
      .then((records: QueryListResultModel<AssessmentGridModel>) => {
        setAssessments(records);
        setIsLoading(false);
      });
  };

  // triggers whenever user takes an action on dialog either 'Save' or 'Close'
  const onDialogClose = (
    action: DialogAction,
    assessmentId?: string,
    country?: string
  ) => {
    setOpen(false);
    setAssessmentId('');
    if (action === DialogAction.Add && assessmentId && country) {
      navigate('/assessment/scope-definition/strategy', {
        state: {
          assessmentId,
          country,
          approach: AssessmentApproach.Rapid,
        },
      });
    }
    if (action === DialogAction.Edit) {
      bindAssessments();
    }
  };

  /// gets the display text for assessment status enums
  const getAssmentStatusDisplayText = (status: AssessmentStatus) => {
    switch (status) {
      case AssessmentStatus.Created:
        return 'Assessment Created';
      case AssessmentStatus.StrategySelected:
        return 'Strategy Selected';
      case AssessmentStatus.TypeSelected:
        return 'Assessment Type Selected';
      case AssessmentStatus.Finalized:
        return 'Finalized';
      case AssessmentStatus.InProgress:
        return 'In Progress';
      case AssessmentStatus.Published:
        return 'Published';
      default:
        return 'Unknown';
    }
  };

  /// gets the display text for assessment user role enums
  const getAssessmentUserRoleDisplayText = (status: AssessmentUserRole) => {
    switch (status) {
      case AssessmentUserRole.Manager:
        return t('Common.Manager');
      case AssessmentUserRole.Editor:
        return t('Common.Editor');
      case AssessmentUserRole.Reviewer:
        return t('Common.Reviewer');
      default:
        return 'N/A';
    }
  };

  // get assessment apporach from enum
  const getAssessmentApproach = (approach: number) => {
    switch (approach) {
      case AssessmentApproach.Rapid:
        return t('Landing.LandingTable2.Rapid');
      case AssessmentApproach.Comphrehensive:
        return t('Landing.LandingTable2.Comprehensive');
      case AssessmentApproach.Tailored:
        return t('Landing.LandingTable2.Tailored');
      default:
        return 'N/A';
    }
  };

  // Open dialog to edit assessment details
  const editAssessmentDetails = (props: GridCellProps) => {
    setAssessmentId(props.dataItem['assessmentId']);
    setAssessmentStatus(props.dataItem['status']);
    setOpen(true);
  };

  // get case strategy type
  const getCaseStrategyType = (stratgeyType: string) => {
    switch (stratgeyType) {
      case StrategiesEnum.BurdenReduction:
        return t('Common.BurdenReduction');
      case StrategiesEnum.Elimination:
        return t('Common.Elimination');
      case StrategiesEnum.Both:
        return t('Common.Both');
      default:
        return t('Common.NotApplicable');
    }
  };

  // generate grid columns for Assessment grid
  const columns: Array<GridColumnProps> = [
    {
      field: 'displayStatus',
      title: t('UserManagement.GridColumn.Status'),
      sortable: true,
      filterable: true,
      cells: {
        data: (props: GridCellProps) => (
          <td>
            {getAssmentStatusDisplayText(props.dataItem['displayStatus'])}
          </td>
        ),
        filterCell: (props: GridFilterCellProps) => (
          <td>
            <Dropdown
              id='approach'
              name='apprpach'
              variant='standard'
              value={props.value}
              options={[
                new MultiSelectModel(
                  AssessmentStatus.InProgress,
                  getAssmentStatusDisplayText(AssessmentStatus.InProgress)
                ),
                new MultiSelectModel(
                  AssessmentStatus.Published,
                  getAssmentStatusDisplayText(AssessmentStatus.Published)
                ),
              ]}
              onChange={(evt: React.ChangeEvent<HTMLInputElement>) =>
                onCustomFilterChange(props, evt)
              }
            />
          </td>
        ),
      },
    },
    {
      field: 'name',
      title: t('UserManagement.GridColumn.Country'),
      filter: 'text',
      sortable: true,
      filterable: true,
      cells: {
        filterCell: (props: GridFilterCellProps) => (
          <td>
            <input
              type="text"
              className="k-textbox"
              value={props.value || ''}
              onChange={(e) => {
                const value = e.target.value;
                props.onChange({
                  value: value,
                  operator: 'contains',
                  syntheticEvent: e
                });
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
            />
          </td>
        )
      }
    },
    {
      field: 'startDate',
      title: t('Common.StartDate'),
      sortable: true,
      filterable: true,
      filter: 'date',
      cells: {
        filterCell: (props: GridFilterCellProps) => (
          <td>
            <DatePicker
              value={props.value ? new Date(props.value) : null}
              onAccept={(value: Date | null) => {
                onCustomDateFilterChange(props, value, null)
              }}
              onChange={(value: Date | null) => {
                onCustomDateFilterChange(props, value, null)
              }}
              error={false}
              helperText=''
              format='yyyy/MM/dd'
              placeholder={t('Common.YearMonthDate')}
            />
          </td>
        ),
      },
    },
    {
      field: 'endDate',
      title: t('Common.EndDate'),
      sortable: true,
      filterable: true,
      filter: 'date',
      cells: {
        filterCell: (props: GridFilterCellProps) => (
          <td>
            <DatePicker
              value={props.value ? new Date(props.value) : null}
              onAccept={(value: Date | null) => {
                onCustomDateFilterChange(props, value, null)
              }}
              onChange={(value: Date | null) => {
                onCustomDateFilterChange(props, value, null)
              }}
              error={false}
              helperText=''
              format='yyyy/MM/dd'
              placeholder={t('Common.YearMonthDate')}
            />
          </td>
        ),
      },
    },
    {
      field: 'caseStrategyType',
      title: t('Common.TransmissionSetting'),
      sortable: true,
      filterable: true,
      cells: {
        data: (props: GridCellProps) => (
          <td>{getCaseStrategyType(props.dataItem['caseStrategyType'])}</td>
        ),
        filterCell: (props: GridFilterCellProps) => (
          <td>
            <Dropdown
              id='caseStrategyType'
              name='caseStrategyType'
              variant='standard'
              value={props.value}
              options={[
                new MultiSelectModel(
                  StrategiesEnum.BurdenReduction,
                  t('Common.BurdenReduction')
                ),
                new MultiSelectModel(
                  StrategiesEnum.Elimination,
                  t('Common.Elimination')
                ),
                /*new MultiSelectModel(StrategiesEnum.Both, t("Common.Both")),*/ //TODO: Need to uncomment later.This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination
              ]}
              onChange={(evt: React.ChangeEvent<HTMLInputElement>) =>
                onCustomFilterChange(props, evt)
              }
            />
          </td>
        ),
      },
    },
    {
      field: 'approach',
      title: t('Assessment.GridColumn.Type'),
      sortable: true,
      filterable: false, //TODO: Need to set true later once below options of filter uncommented. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination

      cells: {
        data: (props: GridCellProps) => (
          <td>{getAssessmentApproach(props.dataItem['approach'])}</td>
        ),
        filterCell: (props: GridFilterCellProps) => (
          <td>
            <Dropdown
              id='approach'
              name='apprpach'
              variant='standard'
              value={props.value}
              options={
                [
                  //TODO: Below lines needs to be uncomment later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination
                  //new MultiSelectModel(AssessmentApproach.Rapid, "Rapid"),
                  //new MultiSelectModel(AssessmentApproach.Tailored, "Tailored"),
                  //new MultiSelectModel(
                  //    AssessmentApproach.Comphrehensive,
                  //    "Comphrehensive"
                  //),
                ]
              }
              onChange={(evt: React.ChangeEvent<HTMLInputElement>) =>
                onCustomFilterChange(props, evt)
              }
            />
          </td>
        ),
      },
    },
  ];

  // get additional grid columns for assessment grid
  const additionalColumns = (): Array<GridColumnProps> => {
    if (+selectedUserType == UserRoleEnum.Viewer) {
      return [
        {
          field: 'role',
          title: t('Assessment.AssessmentRole'),
          sortable: true,
          filterable: true,

          cells: {
            data: (props: GridCellProps) => (
              <td className='d-flex justify-content-start'>
                {getAssessmentUserRoleDisplayText(props.dataItem['role'])}
              </td>
            ),
            filterCell: (props: GridFilterCellProps) => (
              <td>
                <Dropdown
                  id='role'
                  name='role'
                  variant='standard'
                  value={props.value}
                  options={[
                    new MultiSelectModel(
                      AssessmentUserRole.Manager,
                      getAssessmentUserRoleDisplayText(
                        AssessmentUserRole.Manager
                      )
                    ),
                    new MultiSelectModel(
                      AssessmentUserRole.Editor,
                      getAssessmentUserRoleDisplayText(
                        AssessmentUserRole.Editor
                      )
                    ),
                    new MultiSelectModel(
                      AssessmentUserRole.Reviewer,
                      getAssessmentUserRoleDisplayText(
                        AssessmentUserRole.Reviewer
                      )
                    ),
                  ]}
                  onChange={(evt: React.ChangeEvent<HTMLInputElement>) =>
                    onCustomFilterChange(props, evt)
                  }
                />
              </td>
            ),
          },
        },
      ];
    } else {
      return [
        {
          filterable: false,
          cells: {
            data: (props: GridCellProps) => (
              <td>
                {props.dataItem['canConfigure'] && (
                  <MatLink
                    className={
                      hover && hover === props.dataItem['assessmentId']
                        ? 'app-btn-secondary'
                        : 'app-btn-secondary app-btn-secondary-grey'
                    }
                    onClick={() => {
                      editAssessmentDetails(props);
                    }}
                    title={t('Assessment.UpdateAssessmentTitle')}
                  >
                    {t('Common.Edit')}
                  </MatLink>
                )}
              </td>
            ),
          },
        },
      ];
    }
  };

  // all assessment grid columns
  const allColumns: Array<GridColumnProps> = [
    ...columns,
    ...additionalColumns(),
  ];

  // redirect user to the scope definition when clicked on grid row
  const onRowClicked = (event: GridRowClickEvent) => {
    //check for viewer role and restrict to move forward
    if (!event.dataItem['canViewDetails'] && !event.dataItem['canConfigure']) {
      return;
    }

    let url: string = '';
    if (
      event.dataItem['status'] !== AssessmentStatus.Finalized &&
      event.dataItem['status'] !== AssessmentStatus.Published
    ) {
      url = '/assessment/scope-definition/strategy';
    } else {
      url = '/assessment/data-collection/desk-review';
    }

    navigate(url, {
      state: {
        assessmentId: event.dataItem['assessmentId'],
        country: event.dataItem['countryName'],
        approach: event.dataItem['approach'],
        status: event.dataItem['status'],
      },
    });

    const assesmentdate =
      event.dataItem['startDate'] + '-' + event.dataItem['endDate'];
    sessionStorage.setItem(
      Constants.SessionStorageKey.SELECTED_REPORT_YEAR,
      assesmentdate
    );

    {
      /*cleanup redux strategy selection once it goes to the above mentioned URL's on assesment grid row click*/
    }
    dispatch(updateStrategySelection(StrategySelectionModel.init()));
  };

  // triggers whenever user hover on a grid row
  const handleMouseOver = (assessmentId: string) => {
    setHover(assessmentId);
  };

  // triggers whenever user hover out from a grid row
  const handleMouseOut = (assessmentId: string) => {
    setHover('');
  };

  // triggers from grid to manage row rendering
  const onRowrender = (
    trElement: React.ReactElement<HTMLTableRowElement>,
    rowProps: GridRowProps
  ) => {
    const trProps = {
      ...trElement.props,
      ['onMouseOver']: () => handleMouseOver(rowProps.dataItem.assessmentId),
      ['onMouseOut']: () => handleMouseOut(rowProps.dataItem.assessmentId),
      ['className']: classes.rowHeight,
    };

    return React.cloneElement(
      trElement,
      { ...trProps },
      trElement.props.children
    );
  };

  return (
    <section className='page-full-section page-grid-section'>
      <div className='container-fluid'>
        <GridHeader
          headerTitle={t('Assessment.AllAssessments')}
          buttonText={t('Assessment.AddAssessment')}
          onClick={() => {
            setOpen(true);
            setAssessmentStatus(AssessmentStatus.Created);
          }}
          hide={
            assessments.canUserCreateAssessment &&
            (+selectedUserType === UserRoleEnum.SuperManager ||
              +selectedUserType === UserRoleEnum.Manager)
          }
        />
        <div className={classNames(classes.dataWrapper)}>
          {isLoading? <Backdrop className="loader" open={isLoading}>
          <CircularProgress color="inherit" />
        </Backdrop>: <DataGrid
            columns={allColumns}
            filter={initialFilter}
            data={orderBy(assessments.items, sort)}
            sort={sort}
            initialSort={initialSort}
            pageable
            sortable
            take={gridRequestModel.take}
            skip={gridRequestModel.skip}
            total={assessments.totalRows}
            rowRender={onRowrender}
            onRowClick={onRowClicked}
            onFilterChange={onFilterChange}
            onPageChange={onPageChange}
            onSortChange={(e: GridSortChangeEvent) => {
              setSort(e.sort);
              onSortChange(e);
            }}
            hasActionBtn={true}
          />}
         
        </div>

        <Modal
          open={open}
          title={
            !assessmentId
              ? t('Assessment.AddNewAssessmentTitle')
              : t('Assessment.UpdateAssessmentTitle')
          }
          onEscPress={false}
          onDialogClose={() => {
            setOpen(false);
            setAssessmentId('');
          }}
          modalClassName='app-modal-md'
        >
          <AddAssessment
            assessmentId={assessmentId}
            onDialogClose={onDialogClose}
            assessmentStatus={assessmentStatus}
          />
        </Modal>
      </div>
    </section>
  );
};

export default Assessments;
