{"ConnectionStrings": {"DefaultConnection": ""}, "AllowedHosts": "*", "SerilogFilePath": {"FilePath": "FatalLogs/FatalError"}, "SerilogOutputFileTemplate": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}, "Serilog": {"MinimumLevel": {"Default": "Error", "Override": {"Microsoft": "Error", "System": "Error"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithExceptionDetails"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppSettings": {"ShowErrorDetails": true, "SupportUrl": "", "SupportEmail": "", "StatsPollingInterval": 3000, "QueuePollingInterval ": 600000000, "UpdatePasswordLinkExpiry": "48", "CookieExpireTimeSpan": 15, "TokenRefreshThreshold": 300, "RestrictBusinessEntityModification": true, "AzureAD": {"ClientId": "", "ClientSecret": "", "TenantId": "", "BaseUrl": "https://localhost:5001"}, "Email": {"EmailSender": "<EMAIL>", "SmtpHost": "smtp.live.com", "SmtpPort": "587", "UserName": "", "Password": ""}, "SendGrid": {"From": "<EMAIL>", "ApiKey": ""}, "Cloudmersive": {"ApiKey": ""}, "AnalyticsFields": {"TrackingId": "xxxx", "PrivateKey": "xxxx", "ClientEmail": "xxxx", "WebsiteCode": "xxxx"}}}